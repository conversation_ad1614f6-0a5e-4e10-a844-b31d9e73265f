<script setup>

/* 引入pinia数据 */
import {defineSchedule} from '../store/scheduleStore.js'
import {defineUser} from '../store/userStore.js'
let schedule = defineSchedule();
let sysUser = defineUser()

/* 切换到ShowSchedule.vue需要展示当前登录用户的日程 该需求需要用到vue的钩子函数
导入vue生命周期的挂载完毕的钩子函数onMounted
 */
import {onMounted} from 'vue'
/* 获取当前登录用户的日程需要通过axios异步向后端发起请求
导入axios */
import request from '../utils/request.js'
//挂载完毕后，立刻查询当前用户的所有日程信息，赋值给pinia
onMounted( ()=>{
  showSchedule()
})
//调用showSchedule函数会查询当前用户的所有日程信息
async function showSchedule(){
  let {data} = await request.get("/schedule/findAllSchedule",{params:{"uid":sysUser.uid}})
  console.log(data)
  schedule.itemList =data.data.itemList
}
// 新增日程 先为当前用户增加一个空的日程
async function addItem(){
  // 立刻向后端发送一个请求,让后端先为当前用户在数据库中增加一个默认格式的空数据
  let {data} = await request.get("/schedule/addDefaultSchedule",{params:{"uid":sysUser.uid}})
  if(data.code == 200){
    // 增加成功的话调用刷新页面数据方法,立刻获取最新数据
    showSchedule()
  }else{
    alert("添加异常")
  }
}

// 更新日程的方法
async function updateItem(index){
  console.log(schedule.itemList[index])
  // 根据索引获取元素
  // 找到要修改的数据发送给服务端，更新进入数据库即可
  // 将元素通过 JSON串的形式 发送给服务端
  let {data} =await request.post("/schedule/updateSchedule",schedule.itemList[index])
  if(data.code == 200){
    // 服务端修改完毕后,刷新页面数据
    showSchedule()
    alert("更新成功")
  }else{
    alert("更新异常")
  }

}

// 删除日程的方法
async function removeItem(index){
  console.log(schedule.itemList[index])
  // 弹窗提示是否删除
  if(confirm("确定要删除该条数据")){
    // 根据索引获取要删除的item的id
    let sid = schedule.itemList[index].sid
    // 向服务端发送请求删除元素
    let{data} = await request.get("/schedule/removeSchedule",{params:{"sid":sid}})
    //根据业务码判断删除是否成功
    if(data.code == 200){
      // 删除成功,更新数据
      showSchedule()
      alert("删除成功")
    }else{
      // 删除失败,提示失败
      alert("删除失败")
    }
  }
}

</script>

<template>
  <div>
    <h3 class="ht">您的日程如下</h3>
    <table class="tab" cellspacing="0px">
      <tr class="ltr">
        <th>编号</th>
        <th>内容</th>
        <th>进度</th>
        <th>操作</th>
      </tr>
      <tr class="ltr" v-for="(item,index) in schedule.itemList">
        <td v-text="index+1"></td>
        <td>
          <input type="text" class="ipt" v-model="item.title"></input>
        </td>
        <td>
          <input type="radio" v-model="item.completed" value=0>未完成
          <input type="radio" v-model="item.completed" value=1>已完成
        </td>
        <td class="buttonContainer">
          <button class="btn1" @click="removeItem(index)">删除</button>
          <button class="btn1" @click="updateItem(index)">保存修改</button>
        </td>
      </tr>
      <tr class="ltr buttonContainer" >
        <td colspan="4">
          <button class="btn1" @click="addItem()">新增日程</button>
        </td>

      </tr>
    </table>
  </div>
</template>

<style scoped>

.ht{
  text-align: center;
  color: cadetblue;
  font-family: 幼圆;
}
.tab{
  width: 80%;
  border: 5px solid cadetblue;
  margin: 0px auto;
  border-radius: 5px;
  font-family: 幼圆;
}
.ltr td{
  border: 1px solid  powderblue;

}
.ipt{
  border: 0px;
  width: 99%;

}
.btn1{
  border: 2px solid powderblue;
  border-radius: 4px;
  width:100px;
  background-color: antiquewhite;

}
#usernameMsg , #userPwdMsg {
  color: gold;
}

.buttonContainer{
  text-align: center;
}

</style>
