<script setup>
import {ref, reactive} from 'vue'
/* 导入发送请求的axios对象 */
import request from'../utils/request'

//导入vue-router
import {useRouter} from 'vue-router'
/* 导入路由对象 为响应式路由准备*/
const router = useRouter()

// 响应式数据,保存用户输入的表单信息
let registUser = reactive({
  username: '',
  userPwd: ''
})

// 响应式数据,保存校验的提示信息
let reUserPwd = ref('')
let reUserPwdMsg = ref('')
let usernameMsg = ref('')
let userPwdMsg = ref('')

// 校验用户名的方法
async function checkUsername() {
  // 定义正则
  let usernameReg = /^[a-zA-Z0-9]{5,10}$/
  // 校验
  if (!usernameReg.test(registUser.username)) {
    // 提示
    usernameMsg.value = "不合法"
    return false
  }

  // 向后端发送请求,校验用户名是否已经存在
  try {
    // 发送请求,校验用户名是否已经存在
    let {data} = await request.get(
        '/user/checkUsernameUsed',
        {
          params:{username: registUser.username}
    })
    console.log(data)
    // 判断用户名是否存在
    if (data.code != 200) {
      // 提示
      usernameMsg.value = "用户名已经存在"
      return false
    }
  } catch (error) {
    console.log(error)
  }

  // 通过校验
  usernameMsg.value = "OK"
  return true
}

// 校验密码的方法
function checkUserPwd() {
  // 定义正则
  let passwordReg = /^[0-9]{6}$/
  // 校验
  if (!passwordReg.test(registUser.userPwd)) {
    // 提示
    userPwdMsg.value = "不合法"
    return false
  }
  // 通过校验
  userPwdMsg.value = "OK"
  return true
}

// 校验密码的方法
function checkReUserPwd() {
  // 定义正则
  let passwordReg = /^[0-9]{6}$/
  // 校验
  if (!passwordReg.test(reUserPwd.value)) {
    // 提示
    reUserPwdMsg.value = "不合法"
    return false
  }
  console.log(registUser.userPwd, reUserPwd.value)
  // 校验
  if (!(registUser.userPwd == reUserPwd.value)) {
    // 提示
    reUserPwdMsg.value = "不一致"
    return false
  }

  // 通过校验
  reUserPwdMsg.value = "OK"
  return true
}

// 注册的方法
async function regist(){
  // 校验所有的输入框内容是否合法
  let flag1 =await checkUsername()
  let flag2 = checkUserPwd()
  let flag3 = checkReUserPwd()
  if(flag1 && flag2 && flag3){
    let  {data}= await request.post("user/regist",registUser)
    console.log(data)
    if(data.code == 200){
      // 注册成功 编程式路由切换到登录页
      alert("注册成功,快去登录吧")
      router.push("/login")
    }else{
      //同时有人注册同一个用户名 被抢先注册了
      alert("抱歉,用户名被抢注了")
    }
  }else{
    alert("校验不通过,请求再次检查数据")
  }
}

// 清空表单的方法
function clearForm(){
  registUser.username=""
  registUser.userPwd=""
  usernameMsg.value=""
  userPwdMsg.value=""
  reUserPwd.value=""
  reUserPwdMsg.value=""

}

</script>

<template>
  <div>
    <h3 class="ht">请注册</h3>

    <table class="tab" cellspacing="0px">
      <tr class="ltr">
        <td>请输入账号</td>
        <td>
          <input class="ipt"
                 id="usernameInput"
                 type="text"
                 name="username"
                 v-model="registUser.username"
                 @blur="checkUsername()">

          <span id="usernameMsg" class="msg" v-text="usernameMsg"></span>
        </td>
      </tr>
      <tr class="ltr">
        <td>请输入密码</td>
        <td>
          <input class="ipt"
                 id="userPwdInput"
                 type="password"
                 name="userPwd"
                 v-model="registUser.userPwd"
                 @blur="checkUserPwd()">
          <span id="userPwdMsg" class="msg" v-text="userPwdMsg"></span>
        </td>
      </tr>
      <tr class="ltr">
        <td>确认密码</td>
        <td>
          <input class="ipt"
                 id="reUserPwdInput"
                 type="password"
                 v-model="reUserPwd"
                 @blur="checkReUserPwd()">
          <span id="reUserPwdMsg" class="msg" v-text="reUserPwdMsg"></span>
        </td>
      </tr>
      <tr class="ltr">
        <td colspan="2" class="buttonContainer">
          <input class="btn1" type="button" value="注册" @click="regist()">
          <input class="btn1" type="button" value="重置" @click="clearForm()">
          <router-link to="/login">
            <button class="btn1">去登录</button>
          </router-link>
        </td>
      </tr>
    </table>


  </div>
</template>

<style scoped>
.ht {
  text-align: center;
  color: cadetblue;
  font-family: 幼圆;
}

.tab {
  width: 500px;
  border: 5px solid cadetblue;
  margin: 0px auto;
  border-radius: 5px;
  font-family: 幼圆;
}

.ltr td {
  border: 1px solid powderblue;

}

.ipt {
  border: 0px;
  width: 50%;

}

.btn1 {
  border: 2px solid powderblue;
  border-radius: 4px;
  width: 60px;
  background-color: antiquewhite;

}

.msg {
  color: gold;
}

.buttonContainer {
  text-align: center;
}
</style>
