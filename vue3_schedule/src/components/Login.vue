<script setup>
import{ ref,reactive} from 'vue'
/* 导入发送请求的axios对象 */
import request from'../utils/request'

//导入vue-router
import {useRouter} from 'vue-router'
/* 导入路由对象 为响应式路由准备*/
const router = useRouter()

/* 导入pinia中的user对象 */
import {defineUser} from '../store/userStore.js'
//拿到user对象
let userPinia= defineUser()

// 响应式数据,保存用户输入的表单信息
let loginUser =reactive({
  username:'',
  userPwd:''
})

// 响应式数据,保存校验的提示信息
let usernameMsg =ref('')
let userPwdMsg = ref('')

// 校验用户名的方法
function checkUsername(){
  // 定义正则
  var usernameReg=/^[a-zA-Z0-9]{5,10}$/
  // 校验用户名
  if(!usernameReg.test(loginUser.username)){
    // 格式不合法
    usernameMsg.value="格式有误"
    return false
  }
  usernameMsg.value="ok"
  return true
}
// 校验密码的方法
function checkUserPwd(){
  // 定义正则
  var passwordReg=/^[0-9]{6}$/
  // 校验密码
  if(!passwordReg.test(loginUser.userPwd)){
    // 格式不合法
    userPwdMsg.value="格式有误"
    return false
  }
  userPwdMsg.value="ok"
  return true
}

// 登录的方法
async function login(){

  // 校验所有的输入框内容是否合法
  let flag1 =checkUsername()
  let flag2 = checkUserPwd()
  if(flag1 && flag2){
    //输入合法向后端发起请求 获得json串响应
    let {data}= await request.post("user/login",loginUser)
    console.log(data)
    // data.code为501说明账号不存在 为503说明密码错误 为200说明登录成功
    // 判断是否登录成功
    // 登录成功
    if(data.code == 200){
      alert("登录成功,查看您的日程吧")
      // 将登录成功的uid和username保存到user pinia中
      // data中是一个map key为loginUser v为用户信息对象
      console.log(data)
      //下面这两种都可赋值成功
      Object.assign(userPinia,data.data.loginUser)
      // userPinia.uid=data.data.loginUser.uid
      // userPinia.username=data.data.loginUser.username
      console.log(userPinia)
      console.log(userPinia.uid)
      console.log(userPinia.username)


      //编程式路由切换 到展示日程页面
      router.push("/showSchedule")
    }
    if(data.code == 501){
      // 账号不存在 提示信息usernameMsg修改为账号不存在
      usernameMsg.value="账号不存在"
    }else if(data.code == 503){
      // 密码错误 提示信息userPwdMsg修改为密码错误
      userPwdMsg.value="密码错误"
    }

  }
}

// 清空表单的方法
function clearForm(){
  loginUser.username=""
  loginUser.userPwd=""
  usernameMsg.value=""
  userPwdMsg.value=""

}
</script>

<template>
  <div>
    <h3 class="ht">请登录</h3>
    <table class="tab" cellspacing="0px">
      <tr class="ltr">
        <td>请输入账号</td>
        <td>
          <input class="ipt"
                 type="text"
                 v-model="loginUser.username"
                 @blur="checkUsername()">
          <span id="usernameMsg" v-text="usernameMsg"></span>
        </td>
      </tr>
      <tr class="ltr">
        <td>请输入密码</td>
        <td>
          <input class="ipt"
                 type="password"
                 v-model="loginUser.userPwd"
                 @blur="checkUserPwd()">
          <span id="userPwdMsg" v-text="userPwdMsg"></span>
        </td>
      </tr>
      <tr class="ltr">
        <td colspan="2" class="buttonContainer">
          <input class="btn1" type="button" value="登录" @click="login()">
          <input class="btn1" type="button" value="重置" @click="clearForm()">
          <router-link to="/regist">
            <button class="btn1">去注册</button>
          </router-link>
        </td>
      </tr>
    </table>
  </div>
</template>

<style scoped>
.ht{
  text-align: center;
  color: cadetblue;
  font-family: 幼圆;
}
.tab{
  width: 500px;
  border: 5px solid cadetblue;
  margin: 0px auto;
  border-radius: 5px;
  font-family: 幼圆;
}
.ltr td{
  border: 1px solid  powderblue;
}
.ipt{
  border: 0px;
  width: 50%;
}
.btn1{
  border: 2px solid powderblue;
  border-radius: 4px;
  width:60px;
  background-color: antiquewhite;
}
#usernameMsg , #userPwdMsg {
  color: gold;
}
.buttonContainer{
  text-align: center;
}
</style>
