import {createRouter,createWebHashHistory} from 'vue-router'


import Login from '../components/Login.vue'
import Regist from '../components/Regist.vue'
import ShowScedule from '../components/ShowSchedule.vue'

/*
    路由中导入全局pinia的固定写法就是下面这样
 */
import pinia from '../pinia.js'
import {defineUser} from '../store/userStore.js'
let sysUser = defineUser(pinia)

let  router = createRouter({
    history:createWebHashHistory(),
    routes:[
        {
            path:"/",
            component:Login
        },
        {
            path:"/login",
            component:Login
        },
        {
            path:"/showSchedule",
            component:ShowScedule
        },
        {
            path:"/regist",
            component:Regist
        }


    ]
})
/* 配置路由的全局前置守卫,通过userPinia的属性判断用户是否登录从而决定
能否访问showSchedule.vue */
router.beforeEach( (to,from,next) =>{
    // 如果是查看日程 /showSchedule
    if(to.path=="/showSchedule"){
        // 如果尚未的登录 sysUser.username就是空串
        if(sysUser.username == ''){
            //提示未登录再跳切换到登录页
            alert("您尚未登录,请登录后再查看日程")
            //页面切换后改函数还会往下执行所以需要return结束
            next("/login")
            return
        }
    }
    // 如果访问的是其他资源或已经登录了直接放行
    next()
})

export default router