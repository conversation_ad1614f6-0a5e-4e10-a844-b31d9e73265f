/*
专门用于存储日程状态信息的pinia
*/
import {defineStore} from 'pinia'

export const defineSchedule = defineStore('scheduleList',{
    state:()=>{
        return {
            // 存储日程列表 一个用户可能有很多日程
            itemList:[
                {
                    sid:1,
                    uid:1,
                    title:'学java',
                    completed:1
                },
                {
                    sid:2,
                    uid:1,
                    title:'学前端',
                    completed:0
                }
            ]
        }
    },
    getters :{

    },
    actions:{

    }

})