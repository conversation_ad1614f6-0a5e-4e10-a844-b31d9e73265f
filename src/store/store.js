//定义共享的pinia数据
//导入pinia的defineStore函数
import {defineStore } from 'pinia'

/*
定义数据并且对外暴露
 store就是定义共享状态的包装对象
 内部包含四个属性： id 唯一标识 state 完整类型推理，推荐使用箭头函数 存放的数据 getters 类似属性计算，存储放对数据
 操作的方法  actions 存储数据的复杂业务逻辑方法
 理解： store类似Java中的实体类， id就是类名， state 就是装数据值的属性  getters就是get方法，actions就是对数据操作的其他方法
*/
//定义一个Person共享
export const definedPerson = defineStore(
    'personPinia',
    {	//当前数据的id必须 必须全局唯一
        state:()=>{ // state中用于定义数据
            return { //状态其实就是响应式数据
                username:'张三',
                age:0,
                hobbies:['唱歌','跳舞']
            }
        },
        //定义一个获得数据或者是使用数据计算结果的一些函数，这里函数不要修改数据
        getters:{// 用于定义一些通过数据计算而得到结果的一些方法 一般在此处不做对数据的修改操作
            // getters中的方法可以当做属性值方式使用
            getHobbiesCount(){
                return this.hobbies.length
            },
            // 箭头函数的写法 箭头函数没有自己的this 所以需要把state当参数传入
            getAge:(state) =>{
                return state.age
            }
        },

        actions:{ // 用于定义一些对数据修改的方法
            doubleAge(){
                this.age=this.age*2
            }
        }
    }
)