<script setup type="module">
import { ref} from 'vue';
import { definedPerson} from '../store/store';
// 读取存储的数据
let person= definedPerson()

let hobby = ref('')

</script>

<template>
  <div>
    <h1>operate视图,用户操作Pinia中的数据</h1>
    请输入姓名:<input type="text" v-model="person.username"> <br>
    请输入年龄:<input type="text" v-model="person.age"> <br>
    <!--getters中的方法需要当做对象的属性用，不能直接调用-->
    获取年龄: <span>{{person.getAge}}</span> <br>
    请增加爱好:
    <input type="checkbox" value="吃饭"  v-model="person.hobbies"> 吃饭
    <input type="checkbox" value="睡觉"  v-model="person.hobbies"> 睡觉
    <input type="checkbox" value="打豆豆"  v-model="person.hobbies"> 打豆豆 <br>

    <!-- 事件中调用person的doubleAge()方法 -->
    <button @click="person.doubleAge()">年龄加倍</button> <br>
    <!-- 事件中调用pinia提供的$reset()方法恢复数据的默认值 -->
    <button @click="person.$reset()">恢复默认值</button> <br>
    <!-- 事件中调用$patch方法一次性修改多个属性值 -->
    <button @click="person.$patch({username:'奥特曼',age:100,hobbies:['晒太阳','打怪兽']})">变身奥特曼</button> <br>
    显示pinia中的person数据:{{person}}
  </div>
</template>
<style scoped>
</style>