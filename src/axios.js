import axios from 'axios'


//  创建instance实例
const instance = axios.create({
    // 请求路径的公共前缀路径
    baseURL:'https://api.xygeng.cn/',
    //请求超时时间ms为单位
    timeout:10000
})

//  添加请求拦截
instance.interceptors.request.use(
    // 设置请求头配置信息 请求之前，设置请求信息的方法
    config=>{
        //处理指定的请求头
        console.log("before request")
        config.headers.Accept = 'application/json, text/plain, text/html,*/*'
        //设置完毕之后必须返回config
        return config
    },
    // 设置请求错误处理函数
    error=>{
        console.log("request error")
        //一定要返回一个失败状态的promise
        return Promise.reject(error)
    }
)
// 添加响应拦截器
instance.interceptors.response.use(
    // 设置响应正确时的处理函数
    response=>{
        //响应状态码为200要执行的方法
        //处理相应数据
        console.log("after success response")
        console.log(response)
        // 最后要返回response
        return response
    },
    // 设置响应异常时的处理函数
    error=>{
        console.log("after fail response")
        console.log(error)
        //一定要返回一个失败状态的promise
        return Promise.reject(error)
    }
)
// 默认导出
export default instance